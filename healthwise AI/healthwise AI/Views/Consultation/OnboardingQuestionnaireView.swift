import SwiftUI

struct OnboardingQuestionnaireView: View {
    @State private var step: Int = 1
    @State private var name: String = ""
    @State private var age: String = ""
    @State private var gender: String = "男" // 默认合法tag
    @State private var height: String = ""
    @State private var weight: String = ""
    @State private var bmi: Double = 0.0
    @State private var sleepQuality: String = "一般"
    @State private var diet: [String] = []
    @State private var emotion: String = "平稳"
    @State private var personality: String = ""
    @State private var history: [String] = []
    @State private var familyHistory: [String] = []
    @State private var environment: String = ""
    @State private var specialGroup: String = ""
    @State private var showVoiceInput: Bool = false
    @State private var language: String = "中文"
    @State private var exercise: String = "经常锻炼" // 补全变量，默认合法tag
    @State private var sleepHours: Double = 8.0 // 补全变量
    
    // BMI计算函数
    func calculateBMI() {
        guard let h = Double(height), let w = Double(weight), h > 0 else {
            bmi = 0
            return
        }
        bmi = w / pow(h / 100, 2)
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                backgroundView
                    .ignoresSafeArea()
                
                VStack {
                    ProgressView(value: Double(step), total: 8)
                        .padding()
                    
                    Group {
                        if step == 1 {
                            Step1View(name: $name, age: $age, gender: $gender)
                        } else if step == 2 {
                            Step2View(height: $height, weight: $weight, bmi: $bmi, calculateBMI: calculateBMI)
                        } else if step == 3 {
                            Step3View(exercise: $exercise)
                        } else if step == 4 {
                            Step4View(sleepHours: $sleepHours, sleepQuality: $sleepQuality)
                        } else if step == 5 {
                            Step5View(diet: $diet)
                        } else if step == 6 {
                            Step6View(emotion: $emotion, personality: $personality)
                        } else if step == 7 {
                            Step7View(history: $history, familyHistory: $familyHistory, environment: $environment, specialGroup: $specialGroup)
                        } else if step == 8 {
                            Step8View(showVoiceInput: $showVoiceInput, language: $language)
                        }
                    }
                    
                    Spacer()
                    
                    HStack {
                        if step > 1 {
                            Button("上一步") { step -= 1 }
                                .padding()
                        }
                        Spacer()
                        if step < 8 {
                            Button("下一步") { step += 1 }
                                .padding()
                        } else {
                            Button("完成") {
                                // TODO: 保存数据到 ViewModel
                            }
                            .padding()
                        }
                    }
                }
                .padding()
            }
        }
    }
    
    var backgroundView: some View {
        Group {
            if step == 1 {
                LinearGradient(gradient: Gradient(colors: [.blue.opacity(0.2), .white]), startPoint: .top, endPoint: .bottom)
            } else if step == 2 {
                Step2Background()
            } else if step == 3 {
                Step3Background(bmi: bmi)
            } else if step == 4 {
                Step4Background()
            } else if step == 5 {
                Step5Background()
            } else if step == 6 {
                Step6Background()
            } else if step == 7 {
                LinearGradient(gradient: Gradient(colors: [.gray.opacity(0.18), .white]), startPoint: .top, endPoint: .bottom)
            } else if step == 8 {
                Step8Background()
            } else {
                Color.white
            }
        }
    }
}

struct Step2Background: View {
    var body: some View {
        ZStack {
            LinearGradient(gradient: Gradient(colors: [.purple.opacity(0.18), .white]), startPoint: .top, endPoint: .bottom)
            NavigationLink(destination: StepDetailView(title: "身高体重详情")) {
                Image(systemName: "scalemass")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 180)
                    .opacity(0.18)
            }
        }
    }
}

struct Step3Background: View {
    let bmi: Double
    
    var body: some View {
        ZStack {
            LinearGradient(gradient: Gradient(colors: [.green.opacity(0.18), .white]), startPoint: .top, endPoint: .bottom)
            Text("BMI: \(bmi, specifier: "%.1f")")
                .font(.headline)
                .foregroundColor(.blue)
                .frame(width: 180)
                .opacity(0.08)
        }
    }
}

struct Step4Background: View {
    var body: some View {
        ZStack {
            LinearGradient(gradient: Gradient(colors: [.blue.opacity(0.18), .white]), startPoint: .top, endPoint: .bottom)
            Image(systemName: "moon.stars.fill")
                .resizable()
                .scaledToFit()
                .frame(width: 180)
                .opacity(0.08)
        }
    }
}

struct Step5Background: View {
    var body: some View {
        ZStack {
            LinearGradient(gradient: Gradient(colors: [.green.opacity(0.18), .white]), startPoint: .top, endPoint: .bottom)
            Image(systemName: "leaf.fill")
                .resizable()
                .scaledToFit()
                .frame(width: 180)
                .opacity(0.08)
        }
    }
}

struct Step6Background: View {
    var body: some View {
        ZStack {
            LinearGradient(gradient: Gradient(colors: [.orange.opacity(0.18), .white]), startPoint: .top, endPoint: .bottom)
            Image(systemName: "face.smiling")
                .resizable()
                .scaledToFit()
                .frame(width: 180)
                .opacity(0.08)
        }
    }
}

struct Step8Background: View {
    var body: some View {
        ZStack {
            LinearGradient(gradient: Gradient(colors: [.blue.opacity(0.18), .white]), startPoint: .top, endPoint: .bottom)
            Image(systemName: "mic.fill")
                .resizable()
                .scaledToFit()
                .frame(width: 180)
                .opacity(0.08)
        }
    }
}

struct StepDetailView: View {
    var title: String
    
    var body: some View {
        VStack(spacing: 32) {
            Text(title)
                .font(.largeTitle)
                .bold()
            Text("这里是详情页面内容，可自定义展示更多信息。")
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
    }
}

// Step1View
struct Step1View: View {
    @Binding var name: String
    @Binding var age: String
    @Binding var gender: String
    
    var body: some View {
        VStack(spacing: 16) {
            Text("基本信息")
                .font(.title2)
            TextField("姓名", text: $name)
                .textFieldStyle(RoundedBorderTextFieldStyle())
            TextField("年龄", text: $age)
                .keyboardType(.numberPad)
                .textFieldStyle(RoundedBorderTextFieldStyle())
            Picker("性别", selection: $gender) {
                Text("男").tag("男")
                Text("女").tag("女")
                Text("其他").tag("其他")
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
}

// Step2View
struct Step2View: View {
    @Binding var height: String
    @Binding var weight: String
    @Binding var bmi: Double
    var calculateBMI: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Text("身高体重")
                .font(.title2)
            TextField("身高(cm)", text: $height)
                .keyboardType(.decimalPad)
                .textFieldStyle(RoundedBorderTextFieldStyle())
            TextField("体重(kg)", text: $weight)
                .keyboardType(.decimalPad)
                .textFieldStyle(RoundedBorderTextFieldStyle())
            Text("BMI: \(bmi, specifier: "%.1f")")
                .font(.headline)
                .foregroundColor(.blue)
        }
        .onChange(of: height) { _ in calculateBMI() }
        .onChange(of: weight) { _ in calculateBMI() }
    }
}

// Step3View
struct Step3View: View {
    @Binding var exercise: String
    
    var body: some View {
        VStack(spacing: 16) {
            Text("运动量")
                .font(.title2)
            Picker("运动频率", selection: $exercise) {
                Text("经常锻炼").tag("经常锻炼")
                Text("偶尔锻炼").tag("偶尔锻炼")
                Text("久坐").tag("久坐")
                Text("散步").tag("散步")
            }
            .pickerStyle(MenuPickerStyle())
        }
    }
}

// Step4View
struct Step4View: View {
    @Binding var sleepHours: Double
    @Binding var sleepQuality: String
    
    var body: some View {
        VStack(spacing: 16) {
            Text("睡眠质量")
                .font(.title2)
            HStack {
                Text("时长: \(sleepHours, specifier: "%.1f")h")
                Slider(value: $sleepHours, in: 0...12, step: 0.5)
            }
            Picker("深浅", selection: $sleepQuality) {
                Text("深度").tag("深度")
                Text("一般").tag("一般")
                Text("浅睡").tag("浅睡")
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
}

// Step5View
struct Step5View: View {
    @Binding var diet: [String]
    
    var body: some View {
        VStack(spacing: 16) {
            Text("饮食习惯")
                .font(.title2)
            ForEach(["偏甜", "重油", "素食", "清淡", "高蛋白"], id: \.self) { item in
                Toggle(item, isOn: Binding(
                    get: { diet.contains(item) },
                    set: { val in
                        if val {
                            diet.append(item)
                        } else {
                            diet.removeAll { $0 == item }
                        }
                    }
                ))
            }
        }
    }
}

// Step6View
struct Step6View: View {
    @Binding var emotion: String
    @Binding var personality: String
    
    var body: some View {
        VStack(spacing: 16) {
            Text("情绪与性格")
                .font(.title2)
            Picker("情绪状态", selection: $emotion) {
                Text("焦虑").tag("焦虑")
                Text("压力").tag("压力")
                Text("平稳").tag("平稳")
            }
            .pickerStyle(SegmentedPickerStyle())
            Picker("性格", selection: $personality) {
                Text("外向").tag("外向")
                Text("内向").tag("内向")
                Text("活跃").tag("活跃")
                Text("沉静").tag("沉静")
            }
            .pickerStyle(MenuPickerStyle())
        }
    }
}

// Step7View
struct Step7View: View {
    @Binding var history: [String]
    @Binding var familyHistory: [String]
    @Binding var environment: String
    @Binding var specialGroup: String
    
    var body: some View {
        VStack(spacing: 16) {
            Text("病史与环境")
                .font(.title2)
            Text("既往病史")
            ForEach(["高血压", "糖尿病", "心脏病", "抑郁"], id: \.self) { item in
                Toggle(item, isOn: Binding(
                    get: { history.contains(item) },
                    set: { val in
                        if val {
                            history.append(item)
                        } else {
                            history.removeAll { $0 == item }
                        }
                    }
                ))
            }
            Text("家族病史")
            ForEach(["高血压", "糖尿病", "心脏病", "抑郁"], id: \.self) { item in
                Toggle(item, isOn: Binding(
                    get: { familyHistory.contains(item) },
                    set: { val in
                        if val {
                            familyHistory.append(item)
                        } else {
                            familyHistory.removeAll { $0 == item }
                        }
                    }
                ))
            }
            Picker("生活环境", selection: $environment) {
                Text("高原").tag("高原")
                Text("潮湿").tag("潮湿")
                Text("干燥").tag("干燥")
                Text("城市").tag("城市")
            }
            .pickerStyle(MenuPickerStyle())
            Picker("特殊人群", selection: $specialGroup) {
                Text("儿童").tag("儿童")
                Text("孕妇").tag("孕妇")
                Text("老年人").tag("老年人")
                Text("无").tag("")
            }
            .pickerStyle(MenuPickerStyle())
        }
    }
}

// Step8View
struct Step8View: View {
    @Binding var showVoiceInput: Bool
    @Binding var language: String
    
    var body: some View {
        VStack(spacing: 16) {
            Text("语音输入与多语言")
                .font(.title2)
            Button(action: { showVoiceInput.toggle() }) {
                Label("语音输入", systemImage: "mic.fill")
            }
            Picker("语言", selection: $language) {
                Text("中文").tag("中文")
                Text("English").tag("English")
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
}
