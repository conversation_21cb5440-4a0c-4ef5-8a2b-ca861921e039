//
//  ContentView.swift
//  healthwise AI
//
//  Created by apple on 2025/7/30.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var userViewModel = UserViewModel()
    
    var body: some View {
        Group {
            if userViewModel.isLoggedIn {
                MainTabView()
                    .environmentObject(userViewModel)
            } else {
                WelcomeView()
                    .environmentObject(userViewModel)
            }
        }
    }
}

#Preview {
    ContentView()
}
