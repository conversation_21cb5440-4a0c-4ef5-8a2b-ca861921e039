import SwiftUI

struct HealthRemindersView: View {
    @State private var reminders: [HealthReminder] = [
        HealthReminder(title: "喝水提醒", time: "每2小时", isEnabled: true),
        HealthReminder(title: "运动提醒", time: "每天 15:00", isEnabled: true),
        HealthReminder(title: "服药提醒", time: "每天 08:00", isEnabled: false),
        HealthReminder(title: "睡眠提醒", time: "每天 22:00", isEnabled: true)
    ]
    
    var body: some View {
        List {
            Section {
                ForEach($reminders) { $reminder in
                    ReminderRow(reminder: $reminder)
                }
            } header: {
                Text("我的提醒")
            }
            
            Section {
                Button(action: {
                    // 添加新提醒
                }) {
                    Label("添加新提醒", systemImage: "plus.circle.fill")
                }
            }
        }
        .navigationTitle("提醒设置")
    }
}

struct HealthReminder: Identifiable {
    let id = UUID()
    var title: String
    var time: String
    var isEnabled: Bool
}

struct ReminderRow: View {
    @Binding var reminder: HealthReminder
    
    var body: some View {
        HStack {
            VStack(alignment: .leading) {
                Text(reminder.title)
                    .font(.headline)
                Text(reminder.time)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            Toggle("", isOn: $reminder.isEnabled)
        }
        .contentShape(Rectangle())
        .onTapGesture {
            // 编辑提醒
        }
    }
}

#Preview {
    NavigationView {
        HealthRemindersView()
    }
}
