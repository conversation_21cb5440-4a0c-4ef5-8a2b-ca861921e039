import SwiftUI
import Charts

struct DataAnalysisView: View {
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 健康趋势图表
                VStack(alignment: .leading, spacing: 10) {
                    Text("健康趋势")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    VStack(spacing: 15) {
                        // 这里可以添加图表
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.blue.opacity(0.1))
                            .frame(height: 200)
                            .overlay(
                                Text("健康指标趋势图")
                                    .foregroundColor(.secondary)
                            )
                    }
                    .padding()
                    .background(Color.white)
                    .cornerRadius(12)
                    .shadow(radius: 1)
                }
                
                // 数据统计
                VStack(alignment: .leading, spacing: 10) {
                    Text("数据统计")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 15) {
                        DataCard(title: "平均运动时长", value: "45分钟", trend: "+5%", color: .green)
                        DataCard(title: "平均睡眠时间", value: "7.5小时", trend: "-2%", color: .purple)
                        DataCard(title: "每日步数", value: "8,456步", trend: "+12%", color: .blue)
                        DataCard(title: "饮水量", value: "2000ml", trend: "+8%", color: .cyan)
                    }
                    .padding(.horizontal)
                }
            }
            .padding(.vertical)
        }
        .navigationTitle("数据分析")
    }
}

struct DataCard: View {
    let title: String
    let value: String
    let trend: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.title3)
                .fontWeight(.semibold)
            
            HStack {
                Image(systemName: trend.hasPrefix("+") ? "arrow.up.right" : "arrow.down.right")
                Text(trend)
                    .font(.caption2)
            }
            .foregroundColor(trend.hasPrefix("+") ? .green : .red)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(color.opacity(0.1))
        .cornerRadius(12)
    }
}

#Preview {
    NavigationView {
        DataAnalysisView()
    }
}
