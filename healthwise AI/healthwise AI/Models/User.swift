import Foundation

struct User: Codable, Identifiable {
    var id = UUID()
    var name: String = ""
    var age: Int = 0
    var gender: Gender = .notSpecified
    var role: UserRole = .adult
    var height: Double = 0.0 // cm
    var weight: Double = 0.0 // kg
    var bloodType: BloodType = .unknown
    var familyHistory: [String] = []
    var lifestyle: LifestyleInfo = LifestyleInfo()
    var medicalHistory: [String] = []
    var currentDiseases: [String] = []
    var emotionalState: EmotionalState = .neutral
    var preferredLanguage: Language = .chinese
    
    var bmi: Double {
        guard height > 0, weight > 0 else { return 0 }
        return weight / pow(height / 100, 2)
    }
}

enum Gender: String, CaseIterable, Codable {
    case male = "男性"
    case female = "女性"
    case notSpecified = "未指定"
}

enum UserRole: String, CaseIterable, Codable {
    case child = "儿童"
    case adult = "青年"
    case elderly = "老人"
    case medical = "医务人员"
}

enum BloodType: String, CaseIterable, Codable {
    case a = "A型"
    case b = "B型"
    case ab = "AB型"
    case o = "O型"
    case unknown = "未知"
}

enum EmotionalState: String, CaseIterable, Codable {
    case happy = "愉快"
    case neutral = "平静"
    case anxious = "焦虑"
    case depressed = "抑郁"
    case stressed = "压力大"
}

enum Language: String, CaseIterable, Codable {
    case chinese = "中文"
    case english = "English"
    case uyghur = "ئۇيغۇرچە"
}

struct LifestyleInfo: Codable {
    var sedentary: Bool = false
    var smoking: Bool = false
    var drinking: Bool = false
    var sleepHours: Double = 8.0
    var exerciseFrequency: Int = 0 // times per week
}
