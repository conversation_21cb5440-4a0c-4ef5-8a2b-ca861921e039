import SwiftUI
import Foundation

struct NutritionView: View {
    @EnvironmentObject var userViewModel: UserViewModel
    @State private var selectedMealTime: MealTime = .breakfast
    @State private var showingFoodSearch = false
    
    enum MealTime: String, CaseIterable {
        case breakfast = "早餐"
        case lunch = "午餐"
        case dinner = "晚餐"
        case snack = "零食"
    }
    
    private var dailyCalorieNeeds: Double {
        let height = userViewModel.currentUser.height // 单位：厘米
        let weight = userViewModel.currentUser.weight // 单位：公斤
        let age = userViewModel.currentUser.age
        let gender = userViewModel.currentUser.gender
        
        // 使用Harris-Benedict公式计算基础代谢率(BMR)
        var bmr: Double
        switch gender {
        case .male:
            bmr = 66 + (13.7 * weight) + (5 * height) - (6.8 * Double(age))
        case .female:
            bmr = 655 + (9.6 * weight) + (1.8 * height) - (4.7 * Double(age))
        case .notSpecified:
            // 使用平均值计算
            let maleBMR = 66 + (13.7 * weight) + (5 * height) - (6.8 * Double(age))
            let femaleBMR = 655 + (9.6 * weight) + (1.8 * height) - (4.7 * Double(age))
            bmr = (maleBMR + femaleBMR) / 2.0
        }
        
        // 根据活动水平调整（假设中等活动水平，系数1.55）
        return bmr * 1.55
    }
    
    private var nutritionRecommendations: [String] {
        var recommendations: [String] = []
        let bmi = userViewModel.currentUser.bmi
        
        // 根据BMI提供建议
        if bmi < 18.5 {
            recommendations.append("• 适当增加每日热量摄入，建议每日摄入\(Int(dailyCalorieNeeds))卡路里")
            recommendations.append("• 增加优质蛋白质的摄入，如瘦肉、鱼、蛋、奶制品")
            recommendations.append("• 注意补充碳水化合物，选择全谷物")
        } else if bmi >= 24 {
            recommendations.append("• 控制每日热量摄入，建议不超过\(Int(dailyCalorieNeeds * 0.8))卡路里")
            recommendations.append("• 减少高脂肪、高糖分食物的摄入")
            recommendations.append("• 增加蔬菜水果的比例，选择低脂优质蛋白")
        } else {
            recommendations.append("• 维持均衡饮食，建议每日摄入\(Int(dailyCalorieNeeds))卡路里")
            recommendations.append("• 保持适量的蛋白质、碳水化合物和健康脂肪的摄入")
            recommendations.append("• 多样化搭配，保证营养均衡")
        }
        
        // 根据年龄添加特殊建议
        if userViewModel.currentUser.age > 50 {
            recommendations.append("• 注意补充钙质，预防骨质疏松")
            recommendations.append("• 适当补充维生素D，提高钙质吸收")
        }
        
        // 根据用户病史添加建议
        let conditions = Set(userViewModel.currentUser.medicalHistory)
        if !conditions.isEmpty {
            if conditions.contains("糖尿病") {
                recommendations.append("• 控制碳水化合物摄入，选择低血糖指数食物")
                recommendations.append("• 规律进餐，避免过度饥饿")
            }
            if conditions.contains("高血压") {
                recommendations.append("• 控制钠盐摄入，每日不超过6克")
                recommendations.append("• 增加钾的摄入，多吃蔬果")
            }
            if conditions.contains("高血脂") {
                recommendations.append("• 限制饱和脂肪酸摄入")
                recommendations.append("• 选择富含omega-3的食物，如鱼类")
            }
        }
        
        return recommendations
    }
    
    private var mealSuggestions: [String] {
        switch selectedMealTime {
        case .breakfast:
            return breakfastSuggestions
        case .lunch:
            return lunchSuggestions
        case .dinner:
            return dinnerSuggestions
        case .snack:
            return snackSuggestions
        }
    }
    
    private var breakfastSuggestions: [String] {
        var suggestions = [String]()
        let bmi = userViewModel.currentUser.bmi
        
        if bmi < 18.5 {
            suggestions = [
                "全麦面包配煎蛋和牛奶",
                "燕麦粥搭配坚果和水果",
                "高蛋白酸奶配香蕉和麦片",
                "三明治配牛奶和水果",
                "玉米粥配咸鸭蛋和蔬菜"
            ]
        } else if bmi >= 24 {
            suggestions = [
                "蒸水蛋配全麦面包",
                "低脂酸奶配水果",
                "蔬菜沙拉配鸡蛋",
                "燕麦片配脱脂牛奶",
                "杂粮粥配少量咸菜"
            ]
        } else {
            suggestions = [
                "包子配豆浆和鸡蛋",
                "三明治配牛奶和水果",
                "燕麦粥配坚果",
                "馒头配鸡蛋和牛奶",
                "面包配酸奶和水果"
            ]
        }
        return suggestions
    }
    
    private var lunchSuggestions: [String] {
        var suggestions = [String]()
        let bmi = userViewModel.currentUser.bmi
        
        if bmi < 18.5 {
            suggestions = [
                "米饭配红烧肉和青菜",
                "牛肉面配鸡蛋和蔬菜",
                "三明治配鸡胸肉和沙拉",
                "意大利面配肉酱和蔬菜",
                "炒饭配虾仁和蔬菜"
            ]
        } else if bmi >= 24 {
            suggestions = [
                "藜麦配鸡胸肉和蔬菜",
                "蔬菜沙拉配金枪鱼",
                "糙米饭配清蒸鱼和青菜",
                "蒸鸡胸配西兰花",
                "豆腐配蔬菜"
            ]
        } else {
            suggestions = [
                "米饭配炒菜",
                "面条配肉末茄子",
                "混合沙拉配鸡胸肉",
                "饺子配蔬菜汤",
                "炒饭配青菜"
            ]
        }
        return suggestions
    }
    
    private var dinnerSuggestions: [String] {
        var suggestions = [String]()
        let bmi = userViewModel.currentUser.bmi
        
        if bmi < 18.5 {
            suggestions = [
                "牛排配土豆泥和蔬菜",
                "三文鱼配米饭和沙拉",
                "炒面配肉片和蔬菜",
                "汤面配虾仁和青菜",
                "烤鸡配蔬菜"
            ]
        } else if bmi >= 24 {
            suggestions = [
                "蒸鱼配少量米饭",
                "豆腐汤配蔬菜",
                "鸡肉沙拉",
                "清炒时蔬配虾仁",
                "蒸蛋配青菜"
            ]
        } else {
            suggestions = [
                "米饭配清炒蔬菜",
                "面条配虾仁",
                "饺子配汤",
                "炒饭配青菜",
                "鱼肉配蔬菜"
            ]
        }
        return suggestions
    }
    
    private var snackSuggestions: [String] {
        var suggestions = [String]()
        let bmi = userViewModel.currentUser.bmi
        
        if bmi < 18.5 {
            suggestions = [
                "混合坚果",
                "牛奶配香蕉",
                "酸奶配蓝莓",
                "全麦面包配花生酱",
                "能量棒"
            ]
        } else if bmi >= 24 {
            suggestions = [
                "新鲜水果",
                "低脂酸奶",
                "胡萝卜条",
                "无糖茶",
                "小份果干"
            ]
        } else {
            suggestions = [
                "水果",
                "坚果",
                "酸奶",
                "全麦饼干",
                "玉米"
            ]
        }
        return suggestions
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: DesignSystem.Spacing.lg) {
                    // 用户基本信息概览 - 优化设计
                    VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
                        Text("营养状况概览")
                            .font(DesignSystem.Typography.headline)
                            .foregroundColor(DesignSystem.Colors.primaryText)
                        
                        HStack(spacing: DesignSystem.Spacing.md) {
                            EnhancedNutritionInfoCard(
                                title: "BMI",
                                value: String(format: "%.1f", userViewModel.currentUser.bmi),
                                subtitle: getBMIStatus(userViewModel.currentUser.bmi),
                                color: DesignSystem.Colors.info,
                                icon: "figure.stand"
                            )
                            
                            EnhancedNutritionInfoCard(
                                title: "每日推荐热量",
                                value: "\(Int(dailyCalorieNeeds))",
                                subtitle: "卡路里",
                                color: DesignSystem.Colors.nutrition,
                                icon: "flame.fill"
                            )
                        }
                    }
                    .padding(.horizontal)
                    
                    // 营养建议 - 优化设计
                    VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
                        Text("个性化营养建议")
                            .font(DesignSystem.Typography.headline)
                            .foregroundColor(DesignSystem.Colors.primaryText)
                            .padding(.horizontal)
                        
                        HealthCard {
                            VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                                ForEach(nutritionRecommendations, id: \.self) { recommendation in
                                    HStack(alignment: .top, spacing: DesignSystem.Spacing.sm) {
                                        Image(systemName: "checkmark.circle.fill")
                                            .font(DesignSystem.Typography.caption1)
                                            .foregroundColor(DesignSystem.Colors.success)
                                            .padding(.top, 2)
                                        
                                        Text(recommendation)
                                            .font(DesignSystem.Typography.body)
                                            .foregroundColor(DesignSystem.Colors.primaryText)
                                            .fixedSize(horizontal: false, vertical: true)
                                    }
                                }
                            }
                            .padding(DesignSystem.Spacing.md)
                        }
                        .padding(.horizontal)
                    }
                    
                    // 餐时选择器 - 优化设计
                    VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
                        Text("餐时建议")
                            .font(DesignSystem.Typography.headline)
                            .foregroundColor(DesignSystem.Colors.primaryText)
                            .padding(.horizontal)
                        
                        Picker("选择餐时", selection: $selectedMealTime) {
                            ForEach(MealTime.allCases, id: \.self) { mealTime in
                                Text(mealTime.rawValue).tag(mealTime)
                            }
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        .padding(.horizontal)
                        
                        // 餐时建议列表 - 优化设计
                        HealthCard {
                            VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                                HStack {
                                    Image(systemName: getMealIcon(selectedMealTime))
                                        .font(DesignSystem.Typography.title3)
                                        .foregroundColor(DesignSystem.Colors.nutrition)
                                    
                                    Text("\(selectedMealTime.rawValue)推荐")
                                        .font(DesignSystem.Typography.headline)
                                        .foregroundColor(DesignSystem.Colors.primaryText)
                                    
                                    Spacer()
                                }
                                .padding(.bottom, DesignSystem.Spacing.sm)
                                
                                ForEach(Array(mealSuggestions.enumerated()), id: \.offset) { index, suggestion in
                                    HStack(alignment: .top, spacing: DesignSystem.Spacing.md) {
                                        Text("\(index + 1)")
                                            .font(DesignSystem.Typography.caption1)
                                            .fontWeight(.bold)
                                            .foregroundColor(.white)
                                            .frame(width: 20, height: 20)
                                            .background(DesignSystem.Colors.nutrition)
                                            .clipShape(Circle())
                                        
                                        Text(suggestion)
                                            .font(DesignSystem.Typography.body)
                                            .foregroundColor(DesignSystem.Colors.primaryText)
                                            .fixedSize(horizontal: false, vertical: true)
                                        
                                        Spacer()
                                    }
                                    .padding(.vertical, DesignSystem.Spacing.xs)
                                }
                            }
                            .padding(DesignSystem.Spacing.md)
                        }
                        .padding(.horizontal)
                    }
                }
                .padding(.vertical)
            }
            .navigationTitle("饮食建议")
            .navigationBarTitleDisplayMode(.large)
        }
    }
    
    private func getMealIcon(_ mealTime: MealTime) -> String {
        switch mealTime {
        case .breakfast:
            return "sun.rise.fill"
        case .lunch:
            return "sun.max.fill"
        case .dinner:
            return "moon.fill"
        case .snack:
            return "leaf.fill"
        }
    }
    
    private func getBMIStatus(_ bmi: Double) -> String {
        switch bmi {
        case ..<18.5:
            return "偏瘦"
        case 18.5..<24:
            return "标准"
        case 24..<28:
            return "偏重"
        case 28...:
            return "肥胖"
        default:
            return "未知"
        }
    }
}

struct NutritionView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            NutritionView()
                .environmentObject(UserViewModel())
        }
    }
}

struct EnhancedNutritionInfoCard: View {
    let title: String
    let value: String
    let subtitle: String
    let color: Color
    let icon: String
    
    var body: some View {
        HealthCard(backgroundColor: color.opacity(0.1)) {
            VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
                HStack {
                    Image(systemName: icon)
                        .font(DesignSystem.Typography.title3)
                        .foregroundColor(color)
                    
                    Spacer()
                }
                
                VStack(alignment: .leading, spacing: DesignSystem.Spacing.xs) {
                    Text(title)
                        .font(DesignSystem.Typography.caption1)
                        .foregroundColor(DesignSystem.Colors.secondaryText)
                    
                    Text(value)
                        .font(DesignSystem.Typography.title2)
                        .fontWeight(.bold)
                        .foregroundColor(color)
                    
                    Text(subtitle)
                        .font(DesignSystem.Typography.caption2)
                        .foregroundColor(DesignSystem.Colors.tertiaryText)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding(DesignSystem.Spacing.md)
        }
    }
}
