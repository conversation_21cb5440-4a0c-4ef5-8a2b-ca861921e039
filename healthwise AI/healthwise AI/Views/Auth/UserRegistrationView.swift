import SwiftUI

struct UserRegistrationView: View {
    let selectedLanguage: Language
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userViewModel: UserViewModel
    
    @State private var currentStep = 0
    @State private var user = User()
    
    let totalSteps = 4
    
    var body: some View {
        NavigationView {
            VStack {
                // Progress Bar
                ProgressView(value: Double(currentStep + 1), total: Double(totalSteps))
                    .padding()
                
                // Step Content
                Group {
                    switch currentStep {
                    case 0:
                        RoleSelectionStep(user: $user)
                    case 1:
                        BasicInfoStep(user: $user)
                    case 2:
                        HealthInfoStep(user: $user)
                    case 3:
                        LifestyleStep(user: $user)
                    default:
                        EmptyView()
                    }
                }
                
                Spacer()
                
                // Navigation Buttons
                HStack {
                    if currentStep > 0 {
                        But<PERSON>("上一步") {
                            currentStep -= 1
                        }
                        .padding()
                    }
                    
                    Spacer()
                    
                    Button(currentStep == totalSteps - 1 ? "完成" : "下一步") {
                        if currentStep == totalSteps - 1 {
                            user.preferredLanguage = selectedLanguage
                            userViewModel.currentUser = user
                            userViewModel.saveUser()
                            presentationMode.wrappedValue.dismiss()
                        } else {
                            currentStep += 1
                        }
                    }
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                .padding()
            }
            .navigationTitle("用户注册")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct RoleSelectionStep: View {
    @Binding var user: User
    
    var body: some View {
        VStack(spacing: 20) {
            Text("请选择您的角色")
                .font(.title2)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 20) {
                ForEach(UserRole.allCases, id: \.self) { role in
                    RoleCard(role: role, isSelected: user.role == role) {
                        user.role = role
                    }
                }
            }
        }
        .padding()
    }
}

struct RoleCard: View {
    let role: UserRole
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 10) {
                Image(systemName: iconForRole(role))
                    .font(.system(size: 40))
                    .foregroundColor(isSelected ? .white : .blue)
                
                Text(role.rawValue)
                    .font(.headline)
                    .foregroundColor(isSelected ? .white : .primary)
            }
            .frame(height: 100)
            .frame(maxWidth: .infinity)
            .background(isSelected ? Color.blue : Color.gray.opacity(0.1))
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(Color.blue, lineWidth: isSelected ? 2 : 0)
            )
        }
    }
    
    private func iconForRole(_ role: UserRole) -> String {
        switch role {
        case .child: return "figure.child"
        case .adult: return "person.fill"
        case .elderly: return "figure.walk"
        case .medical: return "stethoscope"
        }
    }
}